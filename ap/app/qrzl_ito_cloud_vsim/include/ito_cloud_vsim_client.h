#ifndef __ITO_CLOUD_VSIM_CLIENT_H__
#define __ITO_CLOUD_VSIM_CLIENT_H__

#include "softap_api.h"  // 包含 slog 函数和日志级别定义

#ifdef __cplusplus
extern "C" {
#endif



typedef unsigned short int WORD;
typedef unsigned char BYTE;

#define LOG_TAG "ITO_CLOUD_VSIM"

// 云卡信息结构体定义（与 ito_cloud_vsim_client.c 中保持一致）
typedef struct {
    char imsi[32];          // IMSI号码 (SIM卡格式)
    char iccid[32];         // ICCID号码 (SIM卡格式)
    char real_imsi[16];     // 真实IMSI号码 (转换后格式)
    char real_iccid[21];    // 真实ICCID号码 (转换后格式)
    char imei[16];          // IMEI号码
    char apn[16];           // APN名称
    char serialNum[64];     // 流水号（login_get_card接口返回）
    int ispId;              // 运营商类型
    int is_valid;           // 信息是否有效
} CloudCardInfo;


// 日志宏定义 - 参考 qrzl_info 的定义方式
#define LOG_E(tag, fmt, args...) \
    do { slog("[" tag "]", SLOG_ERR, "[" tag "][%s-%d]: " fmt "\n", __FUNCTION__, __LINE__, ##args); } while (0)

#define LOG_W(tag, fmt, args...) \
    do { slog("[" tag "]", SLOG_ERR, "[" tag "][%s-%d]: " fmt "\n", __FUNCTION__, __LINE__, ##args); } while (0)

#define LOG_I(tag, fmt, args...) \
    do { slog("[" tag "]", SLOG_ERR, "[" tag "][%s-%d]: " fmt "\n", __FUNCTION__, __LINE__, ##args); } while (0)

#define LOG_D(tag, fmt, args...) \
    do { slog("[" tag "]", SLOG_ERR, "[" tag "][%s-%d]: " fmt "\n", __FUNCTION__, __LINE__, ##args); } while (0)

typedef struct {
    char tcp_ip[32];
    int tcp_port;
    char http_ip[32];
    int http_port;
    int heart_beat_times;
    int flow_upload_times;
    char wifi_name[64];
    char wifi_pwd[64];
} ito_cloud_config_t;

/**
 * @brief 初始化ito云vsim客户端
 *
 * @return 0 表示成功, -1 表示失败.
 */
int ito_cloud_vsim_client_init(void);

/**
 * @brief 停止并清理ito云vsim客户端资源
 *
 */
void ito_cloud_vsim_client_deinit(void);

/**
 * @brief 发送VSIM认证同步请求
 *
 * @param apdu_auth 认证APDU请求数据
 * @param len 请求数据长度
 * @param apdu_rsp 用于存储APDU响应数据的缓冲区
 * @param rsp_buf_size 响应缓冲区大小
 * @return 0 表示成功, -1 表示失败
 */
int ito_cloud_auth_sync(char *apdu_auth, int len, char *apdu_rsp, int rsp_buf_size);

/**
 * @brief 从版本字符串中提取版本代码
 *
 * @param version_string 版本字符串，例如 "MZ804LD1.0_KSBC_COMMON_SL_V2.01.01.02P48U05_04-250703"
 * @return 版本代码字符串，例如 "04"，如果解析失败返回 "00"
 */
const char* get_version_code(const char* version_string);

/**
 * @brief 检查云端连接是否准备好进行鉴权
 *
 * @return 1 表示就绪, 0 表示未就绪.
 */
int ito_cloud_is_ready_for_auth(void);

#ifdef __cplusplus
}
#endif

#endif // __ITO_CLOUD_VSIM_CLIENT_H__