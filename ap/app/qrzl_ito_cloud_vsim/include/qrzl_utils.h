#ifndef __QRZL_APP_UTILS_H_
#define __QRZL_APP_UTILS_H_

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>

#include "softap_api.h"

#if __has_include("zte_vsim_api.h")
#include "zte_vsim_api.h"
#else
#include "zxic_vsim_api.h"
#endif

/**
 * 以下是一些用到的NV
 */
#define NV_SN "sn"
#define NV_IMEI "imei"
#define NV_MAC1 "wifi_mac"
#define NV_SOFT_VERSION "cr_version"

#define NV_SELECTED_NET_BAND "selected_net_band"
#define NV_CURRENT_NET_BAND "current_net_band"

#define NV_QRZL_CLOUD_PROTOCOL_TYPE "qrzl_cloud_protocol_type"
#define NV_QRZL_CLOUD_HTTP_REQUEST_TYPE "qrzl_cloud_http_request_type"
#define NV_QRZL_CLOUD_HTTP_PATH "qrzl_cloud_http_path"
#define NV_QRZL_CLOUD_REQUEST_INTERVAL_TIME "qrzl_cloud_request_interval_time"

#define RSIM_ONLY_STR "RSIM_only"
#define ESIM1_ONLY_STR "ESIM1_only"
#define ESIM2_ONLY_STR "ESIM2_only"

/**
 * 初始化工具的一些内容
 */
void qrzl_utils_init();

/**
 * 销毁初始化生成的内容
 */
void qrzl_utils_destroy();

void qrzl_set_process_name(const char* name);
void qrzl_set_thread_name(const char*name);

#define  qrzl_err(fmt, args...)	\
	do {slog("[qrzl_app]",SLOG_ERR,   "[qrzl_app][%s-%d]: " fmt"\n", __FUNCTION__, __LINE__, ## args);} while (0)


#define  qrzl_log(fmt, args...)	\
	do {slog("[qrzl_app]",SLOG_ERR,   "[qrzl_app][%s-%d]: " fmt"\n", __FUNCTION__, __LINE__, ## args);} while (0)

#define  qrzl_info(fmt, args...)	\
	do {slog("[qrzl_app]",SLOG_ERR,   "[qrzl_app][%s-%d]: " fmt"\n", __FUNCTION__, __LINE__, ## args);} while (0)


int nv_update_net_band();

int nv_update_net_cesq_info();

int nv_update_sn();

// 设备出厂后不会变的数据
typedef struct device_static_data_t
{
    char imei[64];
    char mac[32];
	char soft_version[64];
	char hw_version[64];
	char sn[32];
	char device_type[32];
	char dual_sim[10];
	char adjust_band[128];
	char nvro_esim1_iccid[21];
	char nvro_esim1_imsi[16];
	char nvro_esim2_iccid[21];
	char nvro_esim2_imsi[16];
	char nvro_esim3_iccid[21];
	char nvro_esim3_imsi[16];
	char esim1_mno[16]; // esim1 运营商
	char esim2_mno[16]; // esim2 运营商
	char esim3_mno[16]; // esim3 运营商
} DeviceStaticData;

void update_device_static_data();

/**
 * 获取当前SIM卡的卡槽号
 * 0: sim, 1: esim1, 2: esim2
 * -1: 未知
 */
int get_device_current_sim_index();

/* 设置出厂后会被改变的数据 */
typedef struct device_dynamic_data_t
{
	char remain_power[10]; // 电量百分比
	char board_temperature[10]; // 主板温度

	int user_net_disconn; // 这是一个客户的需求，是否假断网。1: 客户无法上网，网络灯红色，但设备内部可以上网; 0: 正常情况

	uint64_t up_speed_bps; // 上行速率 bps，如果要转 kbps 则 / 1024
	uint64_t down_speed_bps; // 下行速率 bps, 如果要转 kbps 则 / 1024

	uint64_t flux_day_total_bytes; // 这一天用的流量, 单位byte, *8/1024转Kb， *8/1024/1024转Mb
	uint64_t flux_month_total_bytes; // 这个月用的流量, 单位byte, *8/1024转Kb， *8/1024/1024转Mb
	uint64_t realtime_tx_bytes; // 当前开机用的上行流量,单位byte
	uint64_t realtime_rx_bytes; // 当前开机用的下行流量,单位byte
	uint64_t realtime_total_bytes; // 当前开机用的总流量，单位byte


	char current_sim[16];

	uint8_t esim1_net_status; // esim1 是否有网 0: 无网 1: 有网
	uint8_t esim2_net_status; // esim2 是否有网 0: 无网 1: 有网
	uint8_t esim3_net_status; // esim3(rsim)是否有网 0: 无网 1: 有网

	int wifi_enable;
	int wifi_hide;
	char wifi_ssid[64];
	char wifi_key[64];
	char wifi_key_base64[256];
	/**  WiFi安全由鉴权模式和加密方式两个组合
	 *  OPEN = （OPEN + NONE);
	 *  WPA2(AES)-PSK = (WPA2PSK + AES)
	 * 	WPA-PSK/WPA2-PSK = (WPAPSKWPA2PSK + TKIPAES)
	 *  WPA3-Persional = (WPA3Personal + AES)
	 *  WPA2(AES)/WPA3-Personal = (WPA2WPA3 + AES)
	 *  */
	char wifi_auth_mode[32]; // OPEN, WPA2PSK, WPAPSKWPA2PSK, WPA3Personal, WPA2WPA3
	char wifi_encryp_type[32]; //NONE, AES, TKIPAES
	
	int wifi_filter_type; // 无规则: 0, 白名单: 1, 黑名单: 2
	char mac_black_list[180]; // 最多10个mac地址，所以最长179个字符再加上终止符为180
	char mac_white_list[180];
	int conn_num;
	int max_access_num;

	char web_password[64];

	char current_wan_ip[16];
	char apn[512];
	
	char iccid[21];
	char mcc[4];
	char mnc[4];
	char tac[17];
	char lac[17];
	char imsi[32];
	char cid[16]; 
	char pci[16]; // 物理小区ID 暂时没有找到
	char rssi[16];
	char rsrq[16]; // at+cesq
	char sinr[16]; // at+ZSINR
	char net_band[16];
	char lte_rsrp[16];

	// 卡槽状态
	int slot_esim1_is_enable;
	int slot_esim2_is_enable;
	int slot_esim3_is_enable;

} DeviceDynamicData;

void update_device_dynamic_data();

typedef struct esim_fluxstat
{
	uint64_t rsim_flux_total; // 单位byte
	uint64_t esim1_flux_total; // 单位byte
	uint64_t esim2_flux_total; // 单位byte

	uint64_t rsim_flux_day_total; // 单位byte
	uint64_t esim1_flux_day_total; // 单位byte
	uint64_t esim2_flux_day_total; // 单位byte

	uint64_t rsim_flux_month_total; // 单位byte
	uint64_t esim1_flux_month_total; // 单位byte
	uint64_t esim2_flux_month_total; // 单位byte
} EsimFluxStat;

EsimFluxStat get_esim_fluxstat();

/**
 * 获取当前SIM卡的卡槽号，是通过全局变量 g_qrzl_device_dynamic_data.current_sim 中的值来直接判断，不具备实时性，谨慎使用
 * 0: sim, 1: esim1, 2: esim2
 * -1: 未知
 */
int get_device_current_sim_index_by_data();

/**
 * 限制上下行网速, 单位Kbps
 */
int limit_net_speed(uint64_t up_limit, uint64_t down_limit);

/**
 * 获取上行限速值，单位Kbps
 */
uint64_t get_up_limit_net_speed();

/**
 * 获取上行限速值，单位Kbps
 */
uint64_t get_down_limit_net_speed();


typedef struct wifi_config_t
{
	char ssid[64];
	char key[64];
	int enable;
	int hide;
	char auth_mode[32]; // OPEN, WPA2PSK, WPAPSKWPA2PSK, WPA3Personal, WPA2WPA3
	int max_access_num;
}WifiConfig;

/**
 * 初始化wifi_config 的值，总的来说就是把当前全局变量g_qrzl_device_dynamic_data的值复制过来
 */
int init_wifi_config_value(struct wifi_config_t *wifi_config);

/**
 * 更新WiFi配置
 */
int update_wifi_by_config(const struct wifi_config_t *wifi_config);

/**
 * WiFi开关
 * 1: 打开WiFi
 * 0: 关闭WiFi
 */
int wifi_switch(int status);

/**
 * 重启设备
 */
int restart_device();

/**
 * 重置设备，即恢复出厂设置
 */
int reset_device();

/**
 * 关机
 */
int shutdown_device();

/**
 * 更新web后台登陆密码
 */
int update_web_password(const char *password);

typedef struct mac_filter_config_t
{
	int wifi_filter_type; // 无规则: 0, 白名单: 1, 黑名单: 2
	char mac_black_list[180]; // 最多10个mac地址，所以最长179个字符再加上终止符为180
	char mac_white_list[180];
}MacFilterConfig;

/**
 * 初始化值
 */
int init_mac_filter_config_value(struct mac_filter_config_t *mac_filter_config);

/**
 * 更新mac过滤配置
 */
int update_mac_filter_by_config(const struct mac_filter_config_t *mac_filter_config);

/**
 * 设置 lte band
 */
int set_lte_net_band(int band);

/**
 * 切卡
 * sim: 0
 * esim1: 1
 * esim2: 2
 */
int switch_sim_card(int sim_type);

/**
 * 切卡不自动重启
 * sim: 0
 * esim1: 1
 * esim2: 2
 */
int switch_sim_card_not_restart(int sim_type);

/**
 * 获取csq
 */
int get_csq();

/**
 * 0没有匹配的运营商, 1 china_mobile, 2 china_united, 3 china_telecom
 */ 
int get_isp_by_imsi(const char *imsi);

/**
 * 获取运营商名称
 */
int get_isp_name_cn_by_imsi(const char *imsi, char *mno, size_t mno_size);

/**
 * 根据imsi更新nv中的运营商
 * esim_num: 1 esim1, 2 esim2
 */
int nv_set_esim_mno(int esim_num, const char *imsi);

/**
 * 客户定制化需求初始化
 */
void customer_customization_requirements_init();

/**
 * 获取本地时间并根据指定格式返回
 * 参数:
 * format: 时间格式字符串 (如 "%Y-%m-%d %H:%M:%S")
 * buffer: 用于存储时间字符串的缓冲区
 * buffer_size: 缓冲区大小
 * 返回值:
 * 0: 成功
 * -1: 失败
*/
int get_local_time(const char *format, char *buffer, size_t buffer_size);

/**
 * status: 1 让br0断开网络连接, 0: 打开br0的网络连接
 */
int set_network_br0_disconnect(int status);

/**
 * url encode
 */
int url_encode(const char *str, char *encoded);


int qrzl_base64_encode_safe(const char *data, int data_len, char *dest, size_t dest_len);

/**
 * 校验字符串是否是一个合法的ipv4地址
 * 成功返回1
 */
int valid_ipv4(const char *ip);

/**
 * 生成一个随机字符串并存入dest中
 */
void generate_random_string(char *dest, size_t dest_len);


/**
 * 获取设备的运行时间
 */
double get_device_uptime();

/**
 * 获取设备是否在充电
 * 1: 充电
 * 0: 未充电
 */
int get_device_charge_status();

/**
 * sleep函数，单位毫秒
 */
void sleep_ms(uint32_t milliseconds);

/**
 * 获取rsrp 信号值百分比
 */
uint8_t get_rsrp_percentage();

/**
 * 获取当前UTC 时间 单位秒
 */
uint32_t get_now_utc_sec();

/**
 * 获取当前电量百分比
 */
uint8_t get_remain_power();

/**
 * 检测网络状态, 0: 有网络, -1: 无网络
 */
int check_network();

void remove_spaces(char *str);

/**
 * 获取当前上网状态
 */
char *get_current_net_status();

/**
 * 设置卡槽状态
 * disable  0：禁用, 1: 启用
 * slot_number  卡槽号：1->ESIM1 2->ESIM2 3->外置卡
 */
void set_slot_state(int disable, int slot_number);

/**
 * 判断一个字符串是否在另一个字符串中 （保留逗号分隔格式）
 * 例如：ESIM1_only,ESIM2_only,RSIM_only， 判断 ESIM1_only 是否存在
 * 找到返回 1，未找到返回 0
 */
int contains_type(const char *input, const char *target);

/**
 * 向 CSV 字符串中添加子字符串（防止重复）（保留逗号分隔格式）
 */
void add_item_to_csv(char *csv, const char *item);

/**
 * 移除字符串中指定项（保留逗号分隔格式）
 */
void remove_item_from_csv(char *input, const char *target);

/*
 * 将 MAC 地址中的 : 替换为 -
 * 输入示例: "00:1A:2B:3C:4D:5E"
 * 输出示例: "00-1A-2B-3C-4D-5E"
 */
void convert_mac_colon_to_dash(const char *mac_input, char *mac_output, size_t output_size);
void convert_mac_dash_to_colon(const char *mac_input, char *mac_output, size_t output_size);

/**
 * 校验手机号
 */
int is_valid_phone(const char *phone);

#endif