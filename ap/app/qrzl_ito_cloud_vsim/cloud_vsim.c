#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <pthread.h>
#include <string.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <arpa/inet.h>
#include <netinet/in.h>

// SDK头文件
#include "softap_api.h"
#include "nv_api.h"

// 项目专属头文件
#include "qrzl_utils.h"
#include "cloud_vsim.h"
#include "ito_cloud_vsim_client.h"
#include "cjson.h"

// 云卡功能开关
//#define QRZL_CLOUD_VSIM_AUTH

#ifdef QRZL_CLOUD_VSIM_AUTH

// 运营商类型枚举
typedef enum {
    OPERATOR_UNKNOWN = 0,
    OPERATOR_CMCC = 1,
    OPERATOR_CUCC = 2,
    OPERATOR_CTCC = 3
} OperatorType;

// 设备信息结构
typedef struct {
    char imsi[32];
    char iccid[32];
    char imei[16];
    char device_id[64];
    char apn[16];
    OperatorType operator;
    int is_valid;
} DeviceInfo;

static DeviceInfo device_info = {0};

#endif // QRZL_CLOUD_VSIM_AUTH

// 全局变量定义
static cJSON* apdu_root = NULL;
static VsimContext vsim_context = {0};
int g_network_attached = 0; // 网络附着状态标记
int g_reauth_started = 0; // 重新鉴权状态标记

// 辅助函数声明和实现
static void generate_apdu_key(const uint8_t* apdu, uint16_t len, char* key) {
    for (int i = 0; i < len; i++) {
        sprintf(key + 2 * i, "%02X", apdu[i]);
    }
    key[2 * len] = '\0';
}

static int hex_to_bin(const char* hex, uint8_t* bin, uint16_t* bin_len) {
    if (!hex || !bin || !bin_len) return -1;
    size_t hex_len = strlen(hex);
    if (hex_len % 2 != 0) {
        qrzl_err("无效的十六进制长度: %zu", hex_len);
        return -1;
    }
    if (hex_len / 2 > *bin_len) {
        qrzl_err("二进制缓冲区不足 (需要 %zu, 提供 %d)", hex_len / 2, *bin_len);
        return -1;
    }
    *bin_len = hex_len / 2;
    for (size_t i = 0; i < *bin_len; i++) {
        if (sscanf(hex + 2 * i, "%2hhx", &bin[i]) != 1) {
            qrzl_err("十六进制转换失败: %s", hex);
            return -1;
        }
    }
    return 0;
}

static void reset_vsim_context() {
    if (vsim_context.current_path) {
        free(vsim_context.current_path);
        vsim_context.current_path = NULL;
    }
    vsim_context.current_node = apdu_root;
    qrzl_info("VSIM 上下文已重置");
}

static int handle_json_node(cJSON* node, uint8_t* apdu_rsp, uint16_t* apdu_rsp_len) {
    if (!node || !apdu_rsp || !apdu_rsp_len) return -1;
    if (cJSON_IsString(node)) {
        return hex_to_bin(node->valuestring, apdu_rsp, apdu_rsp_len);
    } else if (cJSON_IsObject(node)) {
        cJSON* response = cJSON_GetObjectItem(node, "response");
        if (cJSON_IsString(response)) {
            return hex_to_bin(response->valuestring, apdu_rsp, apdu_rsp_len);
        }
    }
    qrzl_err("无效的 JSON 节点类型");
    return -1;
}

#ifdef QRZL_CLOUD_VSIM_AUTH

static int is_auth_apdu(const uint8_t* apdu, uint16_t len) {
    if (!apdu || len < 2) return 0;
    if (apdu[1] == 0x88) { // AUTHENTICATE
        qrzl_info("检测到AUTHENTICATE命令 INS=0x88");
        return 1;
    }
    return 0;
}

static int send_auth_apdu_to_server(const uint8_t* apdu_req, uint16_t apdu_req_len,
                                   uint8_t* apdu_rsp, uint16_t* apdu_rsp_len,
                                   uint8_t slot) {
    g_reauth_started = 1; // 设置重新鉴权标记
    qrzl_info("检测到重新鉴权操作，设置 g_reauth_started = 1");

    char hex_apdu_req[apdu_req_len * 2 + 1];
    generate_apdu_key(apdu_req, apdu_req_len, hex_apdu_req);

    char hex_apdu_rsp[*apdu_rsp_len * 2 + 1];
    memset(hex_apdu_rsp, 0, sizeof(hex_apdu_rsp));

    // qrzl_info("等待云端连接就绪...");
    // int wait_time = 0;
    // while (!ito_cloud_is_ready_for_auth() && wait_time < 30) {
    //     sleep(1);
    //     wait_time++;
    // }

    // if (!ito_cloud_is_ready_for_auth()) {
    //     qrzl_err("等待云端连接超时 (30s)");
    //     return -1;
    // }

    qrzl_info("云端连接就绪，调用 ito_cloud_auth_sync 进行鉴权...");
    int ret = ito_cloud_auth_sync(hex_apdu_req, strlen(hex_apdu_req),
                                  hex_apdu_rsp, sizeof(hex_apdu_rsp));
    if (ret != 0) {
        qrzl_err("ito_cloud_auth_sync 失败, ret=%d", ret);
        return -1;
    }
    qrzl_info("ito_cloud_auth_sync 成功, 响应: %s", hex_apdu_rsp);
    return hex_to_bin(hex_apdu_rsp, apdu_rsp, apdu_rsp_len);
}

static int is_iccid_read_apdu(const uint8_t* apdu, uint16_t len) {
    if (len >= 5 && apdu[0] == 0x00 && apdu[1] == 0xB0) {
        if (vsim_context.current_path && strstr(vsim_context.current_path, "2FE2")) {
            return 1;
        }
    }
    return 0;
}

static int is_imsi_read_apdu(const uint8_t* apdu, uint16_t len) {
    if (len >= 5 && apdu[0] == 0x00 && apdu[1] == 0xB0) {
        if (vsim_context.current_path && strstr(vsim_context.current_path, "6F07")) {
            return 1;
        }
    }
    return 0;
}

static int generate_dynamic_iccid_response(uint8_t* apdu_rsp, uint16_t* apdu_rsp_len) {
    if (!device_info.is_valid || strlen(device_info.iccid) == 0) return -1;
    char iccid_with_status[64] = {0};
    snprintf(iccid_with_status, sizeof(iccid_with_status), "%s9000", device_info.iccid);
    qrzl_info("使用服务器下发的ICCID: %s", device_info.iccid);
    return hex_to_bin(iccid_with_status, apdu_rsp, apdu_rsp_len);
}

static int generate_dynamic_imsi_response(uint8_t* apdu_rsp, uint16_t* apdu_rsp_len) {
    if (!device_info.is_valid || strlen(device_info.imsi) == 0) return -1;
    char imsi_with_status[64] = {0};
    snprintf(imsi_with_status, sizeof(imsi_with_status), "%s9000", device_info.imsi);
    qrzl_info("使用服务器下发的IMSI: %s", device_info.imsi);
    return hex_to_bin(imsi_with_status, apdu_rsp, apdu_rsp_len);
}

static const char* get_operator_name(OperatorType operator) {
    switch (operator) {
        case OPERATOR_CMCC: return "中国移动";
        case OPERATOR_CUCC: return "中国联通";
        case OPERATOR_CTCC: return "中国电信";
        default: return "未知运营商";
    }
}

static const char* get_operator_json_file(OperatorType operator) {
    switch (operator) {
        case OPERATOR_CMCC: return "/etc/cmcc.json";
        case OPERATOR_CUCC: return "/etc/cucc.json";
        case OPERATOR_CTCC: return "/etc/ctcc.json";
        default: return "/etc/cmcc.json";
    }
}

#endif // QRZL_CLOUD_VSIM_AUTH

static void print_hex(const char* label, const uint8_t* data, uint16_t len) {
    if (!label || !data || len == 0) return;
    char hex_str[3 * len + 1];
    char *ptr = hex_str;
    for (int i = 0; i < len && i < 256; i++) {
        ptr += sprintf(ptr, "%02X ", data[i]);
    }
    *ptr = '\0';
    qrzl_info("%s (长度 %d): %s", label, len, hex_str);
}

static int32_t vsim_event_handler(E_MODEM_EVENT_ID event_id, void *ind_data, uint32_t ind_data_len) {
    qrzl_info("===== 收到 Modem 事件通知 =====");
    qrzl_info("事件 ID: 0x%02X (%d)", event_id, event_id);
    switch (event_id) {
        case E_RF_RESOURCE_CALLBACK_EVENT:
            qrzl_info("事件类型: 射频资源切换");
            break;
        case E_NW_ATTACH_COMMPLETE_EVENT:
            qrzl_info("事件类型: 网络附着完成");
            if (ind_data_len == 1) {
                uint8_t sim_id = (uint8_t)(intptr_t)ind_data;
                qrzl_info("SIM 槽位: %d 已成功附着网络", sim_id);
                
        
            #ifdef QRZL_CLOUD_VSIM_AUTH
                if (1 == sim_id) {
                    // 设置网络附着完成标记
                    g_network_attached = 1;
                    qrzl_info("已设置网络附着状态标记 g_network_attached=1");

                    // 准备拨号参数
                    Data_call_config_info_t data_call_config;
                    memset(&data_call_config, 0, sizeof(Data_call_config_info_t));
                    data_call_config.profile_id = 5; // 使用profile 5
                    data_call_config.apn_name_valid = 1;
                    // 如果有从云端获取的APN，应该在这里使用
                    if (device_info.is_valid && strlen(device_info.apn) > 0) {
                        strncpy(data_call_config.apn_name, device_info.apn, sizeof(data_call_config.apn_name) - 1);
                    } else {
                        // 否则使用默认APN
                        strncpy(data_call_config.apn_name, "3gnet", sizeof(data_call_config.apn_name) - 1);
                    }
                    
                    qrzl_info("发起数据拨号 (Profile ID: %d, APN: %s)...", data_call_config.profile_id, data_call_config.apn_name);
                    int ret = zte_StartDataCallbySimId(&data_call_config, sim_id);
                    if (ret != 0) {
                        qrzl_err("发起数据拨号失败, ret=%d", ret);
                    }
                }
            #endif
            }
            break;
        case E_DATA_PDN_ACT_SUCC_EVENT:
            qrzl_info("事件类型: PDN 激活完成");
            Data_call_addr_info_list_t addr_list;
            memset(&addr_list, 0, sizeof(addr_list));
            if (zte_GetDataAddrbySimID(5, &addr_list, 1) == 0) {
                // 假设我们关心第一个IP地址
                if (addr_list.addr_info_len > 0 && addr_list.addr_info[0].iface_addr_s.addr_valid) {
                    char ip_str[INET6_ADDRSTRLEN] = {0};
                    struct sockaddr_in *sa = (struct sockaddr_in *)&addr_list.addr_info[0].iface_addr_s.addr;
                    inet_ntop(AF_INET, &sa->sin_addr, ip_str, sizeof(ip_str));
                    qrzl_info("获取到IP地址: %s", ip_str);
                }
            } else {
                qrzl_err("获取IP地址失败");
            }
            break;
        case E_GET_CELLINFO_BY_SIMID_EVENT:
            qrzl_info("事件类型: 小区信息获取完成");
            break;
        default:
            qrzl_info("事件类型: 未知事件");
            break;
    }
    qrzl_info("==============================");
    return 0;
}

static int vsim_apdu_handler(uint8_t *apdu_req, uint16_t apdu_req_len,
                             uint8_t *apdu_rsp, uint16_t *apdu_rsp_len,
                             uint8_t slot) {
    char req_key[512] = {0};
    generate_apdu_key(apdu_req, apdu_req_len, req_key);

    qrzl_info("===== [VSIM Slot %d] APDU 请求 =====", slot);
    print_hex("APDU REQ", apdu_req, apdu_req_len);
    qrzl_info("REQ KEY: %s", req_key);

#ifdef QRZL_CLOUD_VSIM_SOFTSIM_MODE
    print_hex("APDU RSP (SOFTSIM MODE)", apdu_rsp, *apdu_rsp_len);
    return 0;
#endif

#ifdef QRZL_CLOUD_VSIM_AUTH
    if (is_auth_apdu(apdu_req, apdu_req_len)) {
        uint16_t auth_apdu_len = apdu_req_len;
        qrzl_info("检测到鉴权APDU，发送到云端");

        // 根据要求，如果最后一个字节是00，则去掉
        if (apdu_req_len > 0 && apdu_req[apdu_req_len - 1] == 0x00) {
            qrzl_info("鉴权APDU最后一个字节为00，将其移除后发送");
            auth_apdu_len--;
        }

        if (send_auth_apdu_to_server(apdu_req, auth_apdu_len, apdu_rsp, apdu_rsp_len, slot) == 0) {
            qrzl_info("云端鉴权成功");
            if (*apdu_rsp_len + 2 <= 256) { // 假设apdu_rsp缓冲区足够大
                apdu_rsp[*apdu_rsp_len] = 0x90;
                apdu_rsp[*apdu_rsp_len + 1] = 0x00;
                *apdu_rsp_len += 2;
            }
            print_hex("APDU RSP (Cloud)", apdu_rsp, *apdu_rsp_len);
            return 0;
        } else {
            qrzl_info("云端鉴权失败，尝试本地处理");
        }
    }
#endif

    if (apdu_req_len >= 7 && apdu_req[0] == 0x00 && apdu_req[1] == 0xA4 &&
        memcmp(apdu_req + 5, "\x3F\x00", 2) == 0) {
        qrzl_info("SELECT MF (3F00) 检测到, 重置上下文");
        reset_vsim_context();
    }

    cJSON* command_context_node = NULL;
    if (vsim_context.current_node && cJSON_IsObject(vsim_context.current_node)) {
        if (cJSON_GetObjectItem(vsim_context.current_node, req_key)) {
            command_context_node = vsim_context.current_node;
        }
    }
    if (!command_context_node) {
        cJSON* apdus_array = cJSON_GetObjectItem(apdu_root, "apdus");
        if (cJSON_IsArray(apdus_array)) {
            int array_size = cJSON_GetArraySize(apdus_array);
            int i;
            for (i = 0; i < array_size; i++) {
                cJSON* apdu_item = cJSON_GetArrayItem(apdus_array, i);
                if (apdu_item && cJSON_GetObjectItem(apdu_item, req_key)) {
                    command_context_node = apdu_item;
                    break;
                }
            }
        }
    }

    if (command_context_node) {
        cJSON* response_node = cJSON_GetObjectItem(command_context_node, req_key);
        if (handle_json_node(response_node, apdu_rsp, apdu_rsp_len) == 0) {
            qrzl_info("成功处理命令: %s", req_key);
            print_hex("APDU RSP", apdu_rsp, *apdu_rsp_len);

#ifdef QRZL_CLOUD_VSIM_AUTH
            if (is_iccid_read_apdu(apdu_req, apdu_req_len)) {
                if (generate_dynamic_iccid_response(apdu_rsp, apdu_rsp_len) == 0) {
                    print_hex("动态ICCID响应", apdu_rsp, *apdu_rsp_len);
                }
            } else if (is_imsi_read_apdu(apdu_req, apdu_req_len)) {
                if (generate_dynamic_imsi_response(apdu_rsp, apdu_rsp_len) == 0) {
                    print_hex("动态IMSI响应", apdu_rsp, *apdu_rsp_len);
                }
            }
#endif
            if (apdu_req[0] == 0x00 && apdu_req[1] == 0xA4) {
                if (vsim_context.current_path) free(vsim_context.current_path);
                vsim_context.current_path = strdup(req_key);
                vsim_context.current_node = command_context_node;
                qrzl_info("上下文更新为: %s", req_key);
            }
            return 0;
        }
    }

    qrzl_err("APDU命令未在JSON表中找到: %s", req_key);

#ifdef QRZL_CLOUD_VSIM_AUTH
    if (is_iccid_read_apdu(apdu_req, apdu_req_len)) {
        if (generate_dynamic_iccid_response(apdu_rsp, apdu_rsp_len) == 0) {
            print_hex("动态ICCID响应", apdu_rsp, *apdu_rsp_len);
            return 0;
        }
    } else if (is_imsi_read_apdu(apdu_req, apdu_req_len)) {
        if (generate_dynamic_imsi_response(apdu_rsp, apdu_rsp_len) == 0) {
            print_hex("动态IMSI响应", apdu_rsp, *apdu_rsp_len);
            return 0;
        }
    }
#endif

    if (*apdu_rsp_len >= 2) {
        apdu_rsp[0] = 0x6A;
        apdu_rsp[1] = 0x82;
        *apdu_rsp_len = 2;
        print_hex("APDU RSP", apdu_rsp, *apdu_rsp_len);
        return 0;
    }
    return -1;
}

static int load_apdu_table(const char* filename) {
    FILE* fp = fopen(filename, "r");
    if (!fp) {
        qrzl_err("无法打开 APDU 文件: %s", filename);
        return -1;
    }
    fseek(fp, 0, SEEK_END);
    long fsize = ftell(fp);
    fseek(fp, 0, SEEK_SET);
    char* json_str = malloc(fsize + 1);
    if (!json_str) {
        fclose(fp);
        qrzl_err("分配内存失败");
        return -1;
    }
    fread(json_str, 1, fsize, fp);
    fclose(fp);
    json_str[fsize] = '\0';
    
    char* json_start = json_str;
    if (fsize >= 3 && (uint8_t)json_str[0] == 0xEF && (uint8_t)json_str[1] == 0xBB && (uint8_t)json_str[2] == 0xBF) {
        json_start += 3;
    }

    if (apdu_root) cJSON_Delete(apdu_root);
    apdu_root = cJSON_Parse(json_start);
    free(json_str);

    if (!apdu_root) {
        qrzl_err("JSON 解析失败: %s", cJSON_GetErrorPtr());
        return -1;
    }
    reset_vsim_context();
    qrzl_info("APDU 表加载成功: %s", filename);
    return 0;
}

static void *qrzl_vsim_thread_handler(void *arg) {
    qrzl_info("VSIM 线程启动");
    CloudCardInfo *card_info = (CloudCardInfo *)arg;

#ifdef QRZL_CLOUD_VSIM_AUTH
    if (card_info && card_info->is_valid) {
        device_info.operator = (OperatorType)card_info->ispId;
        strncpy(device_info.imsi, card_info->imsi, sizeof(device_info.imsi) - 1);
        strncpy(device_info.iccid, card_info->iccid, sizeof(device_info.iccid) - 1);
        strncpy(device_info.imei, card_info->imei, sizeof(device_info.imei) - 1);
        strncpy(device_info.apn, card_info->apn, sizeof(device_info.apn) - 1);
        device_info.is_valid = 1;

        const char *json_file = get_operator_json_file(device_info.operator);
        qrzl_info("加载 %s 运营商APDU配置: %s", get_operator_name(device_info.operator), json_file);
        if (load_apdu_table(json_file) != 0) {
            qrzl_err("加载APDU配置表失败");
        }
    } else {
        qrzl_info("使用默认APDU配置");
        if (load_apdu_table("/etc/cmcc.json") != 0) {
            qrzl_err("加载默认APDU配置表失败");
        }
    }
#else
    if (load_apdu_table("/etc/cmcc.json") != 0) {
        qrzl_err("加载默认APDU配置表失败");
    }
#endif

    const char *imei = "862769025435956"; // 默认 IMEI
    if (card_info && card_info->is_valid && strlen(card_info->imei) > 0) {
        imei = card_info->imei;
    }
    qrzl_info("设置 IMEI: %s", imei);
    int ret = zte_SetIMEIbySimId((uint8_t *)imei, strlen(imei), 1);
    if (ret != 0) {
        qrzl_err("设置 IMEI 失败, 错误码: %d", ret);
    }

    if (card_info && card_info->is_valid && strlen(card_info->apn) > 0) {
        char at_cmd[128] = {0};
        snprintf(at_cmd, sizeof(at_cmd), "AT+CGDCONT=5,\"IP\",\"%s\"\r\n", card_info->apn);
        qrzl_info("设置 APN: %s", card_info->apn);
        ret = zte_SendATbySimId(at_cmd, NULL, NULL, 1);
        if (ret != 0) {
            qrzl_err("设置 APN 失败, 错误码: %d", ret);
        }
    }

    zte_mdm_event_regist(vsim_event_handler);
#ifdef QRZL_CLOUD_VSIM_SOFTSIM_MODE
    zte_atSoftSimInit(vsim_apdu_handler, 1);
#else
    zte_atVsimInit(vsim_apdu_handler, 1);
#endif
    qrzl_info("VSIM 初始化完成");

     // 6. 验证 IMEI 设置
    uint8_t actual_imei[16] = {0};
    ret = zte_GetIMEIbySimId(actual_imei, sizeof(actual_imei), 1);
    if (ret == 0) {
        qrzl_info("IMEI 验证成功: %s", actual_imei);
    } else {
        qrzl_err("IMEI 验证失败, 错误码: %d", ret);
    }


    while (1) {
        sleep(10);
        E_SIM_STATUS sim_status;
        if (zte_GetSimStatusbySimId(&sim_status, 1) == 0) {
            qrzl_info("SIM1 状态: %d", sim_status);
        }

        uint8_t iccid[21] = {0};
        if (zte_GetCCIDbySimId(iccid, sizeof(iccid), 1) == 0) {
            qrzl_info("SIM1 ICCID: %s", iccid);
        }

        char mcc[4] = {0};
        char mnc[4] = {0};
        if (zte_GetMCCMNCbySimId(mcc, mnc, 1) == 0) {
            qrzl_info("SIM1: mcc %s mnc %s", mcc, mnc);
        }

        int32_t rssi = 0, ber = 0;
        if (zte_GetCSQbySimId(&rssi, &ber, 1) == 0) {
            qrzl_info("SIM1 信号强度: RSSI=%d, BER=%d", rssi, ber);
        }

         // 定期获取网络信息
        RegInfo_t reg_info = {0};
        if (zte_GetRegInfobySimid(&reg_info, 1) == 0) {
            qrzl_info("网络状态: RAT=%d, 服务状态=%d",
                      reg_info.curr_rat, reg_info.nStatus);
        }
    }
    
    zte_atVsimClose(vsim_apdu_handler, 1);
    return NULL;
}

void start_vsim_thread(void *arg) {
    pthread_t qrzl_vsim_tid;
    if (pthread_create(&qrzl_vsim_tid, NULL, qrzl_vsim_thread_handler, arg) != 0) {
        qrzl_err("创建 VSIM 线程失败");
    } else {
        qrzl_info("VSIM 线程创建成功");
    }
}