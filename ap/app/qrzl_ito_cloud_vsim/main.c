#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <pthread.h>
#include <string.h>
#include <assert.h>

#include "qrzl_utils.h"
#include "ito_cloud_vsim_client.h"
#include "cloud_vsim.h"

// 全局运行标志
static volatile int g_running = 1;

/**
 * 信号处理函数 - 优雅退出
 */
static void signal_handler(int sig)
{
    qrzl_info("接收到信号 %d，准备退出程序...", sig);
    g_running = 0;
}

/**
 * 程序清理函数 - 释放所有资源
 */
static void cleanup_resources(void)
{
    qrzl_info("开始清理程序资源...");
    ito_cloud_vsim_client_deinit();
    qrzl_info("IOT云VSIM客户端已清理");
    
    // 清理qrzl_utils资源
    qrzl_utils_destroy();
    qrzl_info("qrzl_utils资源已清理");
    
    qrzl_info("程序资源清理完成");
}

/**
 * 注册信号处理函数
 */
static void setup_signal_handlers(void)
{
    signal(SIGTERM, signal_handler);  // 终止信号
    signal(SIGINT, signal_handler);   // 中断信号 (Ctrl+C)
    signal(SIGQUIT, signal_handler);  // 退出信号
    qrzl_info("信号处理函数已注册");
}

/*create message queue*/
static int create_msg_queue(int module_id)
{
    int msq_id = msgget(module_id, IPC_CREAT | 0600);
    if(msq_id == -1) {
        qrzl_err("failed to create msg queue module_id=%d, errno=%d", module_id, errno);
    }
    return msq_id;
}

/*wrapper of msgrcv*/
static ssize_t msg_recv(int msqid, void* msgp, size_t msgsz, long msgtype, int msgflag)
{
    return msgrcv(msqid, msgp, msgsz, msgtype, msgflag);
}

static void handle_vsim_auth(MSG_BUF *msg)
{
    char apdu_auth[256] = {0};
    char apdu_rsp[512] = {0};
    int ret;

    // 从消息中提取APDU认证数据
    memcpy(apdu_auth, msg->aucDataBuf, msg->usDataLen);

    qrzl_log("收到VSIM认证请求, APDU长度: %d", msg->usDataLen);
    qrzl_log("APDU认证数据: %s strlen: %d", apdu_auth, strlen(apdu_auth));

    // 把数据通道切到 待0 （种子卡）
    zte_SetGTDATASIM(0);
    // 调用ito_cloud_auth_sync函数获取响应
    ret = ito_cloud_auth_sync(apdu_auth, strlen(apdu_auth), apdu_rsp, sizeof(apdu_rsp));

    if (ret == 0) {
        qrzl_log("VSIM认证成功，准备发送响应");
        qrzl_log("APDU响应数据: %s", apdu_rsp);

        // 把数据通道切到 待1（云卡）
		zte_SetGTDATASIM(1);

        // 通过ipc_send_message发送响应数据
        // 这里假设有一个响应消息类型，您可能需要根据实际情况调整
        ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_UICC_AGT_SVR, MSG_CMD_UICC_AGT_SVR_VSIM_AUTH_RSP,
                        strlen(apdu_rsp), (unsigned char*)apdu_rsp, 0);
        
        qrzl_log("VSIM认证响应已发送");
    } else {
        qrzl_err("VSIM认证失败，错误码: %d", ret);

         // 把数据通道切到 待1
		zte_SetGTDATASIM(1);
        qrzl_log("VSIM认证错误响应已发送");

        // 发送错误响应
        const char* error_rsp = "AUTH_FAILED";
        ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_UICC_AGT_SVR, MSG_CMD_UICC_AGT_SVR_VSIM_AUTH_RSP,
                        strlen(error_rsp), (unsigned char*)error_rsp, 0);
    
    }
}

/* 主消息处理线程 */
static void handle_msg(MSG_BUF *msg)
{
    assert(msg);

    switch(msg->usMsgCmd) {
    case MSG_CMD_QRZL_APP_ITO_VSIM_AUTH:
        handle_vsim_auth(msg);
        break;
    default:
        qrzl_log("未知消息命令: %d", msg->usMsgCmd);
    }
}

int main(int argc, char *argv[])
{
    int vsim_msq = -1;
    int ret;
    MSG_BUF msg;

    qrzl_info("qrzl_ito_cloud_vsim start version v1.0-20250715");

    // 初始化工具库
    qrzl_utils_init();
    qrzl_set_process_name("qrzl_ito_cloud_vsim");

    // 注册信号处理
    setup_signal_handlers();

    // 创建消息队列
    vsim_msq = create_msg_queue(MODULE_ID_QRZL_APP);
    if(vsim_msq < 0) {
        qrzl_err("创建VSIM消息队列失败");
        return -1;
    }

    // 初始化VSIM客户端
    ret = ito_cloud_vsim_client_init();
    if (ret != 0) {
        qrzl_err("VSIM客户端初始化失败, 错误码: %d", ret);
        return -1;
    }

    qrzl_info("VSIM服务启动成功，进入主循环...");

    while (g_running)
    {
        memset(&msg, 0x00, sizeof(MSG_BUF));
        ret = msg_recv(vsim_msq, &msg, (sizeof(MSG_BUF) - sizeof(long)), 0, 1);
        
        if(ret <= 0) {
            // 超时或错误，检查是否需要退出
            if (!g_running) break;
            continue;
        }

        qrzl_log("收到消息命令: %d", msg.usMsgCmd);
        handle_msg(&msg);
    }

    qrzl_info("VSIM服务停止，开始清理资源...");
    cleanup_resources();
    
    qrzl_info("程序正常退出");
    return 0;
}
