include $(COMMON_MK)

# 独立可执行文件Makefile
TARGET = qrzl_ito_cloud_vsim
SRCS = ito_cloud_vsim_client.c cloud_vsim.c main.c qrzl_utils.c cjson.c
OBJS = $(SRCS:.c=.o)

# 使用相对路径指向原项目库目录
ZTE_LIB_PATH = $(zte_lib_path)

CFLAGS += -DQRZL_CLOUD_VSIM_AUTH
#CFLAGS += -DQRZL_CLOUD_VSIM_SOFTSIM_MODE
CFLAGS += -std=gnu99
CFLAGS += -I$(APP_DIR)/include
CFLAGS += -I./include
CFLAGS += -I$(zte_lib_path)/libsoft_timer
CFLAGS += -I$(zte_lib_path)/libnvram
CFLAGS += -I$(zte_lib_path)/libsoftap
CFLAGS += -I$(zte_lib_path)/libatutils
CFLAGS += -I$(zte_lib_path)/libsoftap/include
CFLAGS += -I$(zte_lib_path)/libcurl/install/include
CFLAGS += -I$(zte_lib_path)/libvsim



LDLIBS = -lsoftap -lsoft_timer -latutils -lnvram -lcurl -lm -lght_vsim -lpthread
LDFLAGS = -L$(ZTE_LIB_PATH)/libsoftap \
          -L$(ZTE_LIB_PATH)/libsoft_timer \
          -L$(ZTE_LIB_PATH)/libatutils \
          -L$(ZTE_LIB_PATH)/libnvram \
          -L$(ZTE_LIB_PATH)/libcurl/install/lib/ \
          -L$(ZTE_LIB_PATH)/libvsim

all: $(TARGET)

$(TARGET): $(OBJS)
	$(CC) $(LDFLAGS) -o $@ $^ $(LDLIBS)

romfs:
	$(ROMFSINST) /bin/qrzl_ito_cloud_vsim
	$(ROMFSINST) data/vsim_apdu.json /etc/vsim_apdu.json
	$(ROMFSINST) data/cmcc.json /etc/cmcc.json
	$(ROMFSINST) data/ctcc.json /etc/ctcc.json
	$(ROMFSINST) data/cucc.json /etc/cucc.json


clean:
	rm -f $(OBJS) $(TARGET)
